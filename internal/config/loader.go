// internal/config/loader.go
package config

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// Load 載入配置，支援多層次配置來源
func Load() (*Config, error) {
	// 建立預設配置
	cfg := defaultConfig()

	// 嘗試載入配置檔案
	if err := loadFromFile(cfg); err != nil {
		// 如果配置檔案不存在，使用預設配置
		fmt.Printf("Warning: Could not load config file, using defaults: %v\n", err)
	}

	// 從環境變數覆蓋配置
	loadFromEnv(cfg)

	// 驗證配置
	if err := validate(cfg); err != nil {
		return nil, fmt.Errorf("config validation failed: %v", err)
	}

	return cfg, nil
}

// defaultConfig 返回預設配置
func defaultConfig() *Config {
	return &Config{
		App: AppConfig{
			Name:        "Koopa's Development Assistant",
			Version:     "1.0.0",
			LogLevel:    "info",
			Environment: "development",
		},
		Modules: ModulesConfig{
			K8s: K8sConfig{
				ConnectionType:    K8sConnectionAuto,
				KubeConfigPath:    filepath.Join(os.Getenv("HOME"), ".kube", "config"),
				CurrentContext:    "",
				Namespace:         "default",
				AutoRetry:         false, // 預設不自動重試以避免重複錯誤
				RetryInterval:     30,    // 30 秒重試間隔
				ConnectionTimeout: 10,    // 10 秒連線超時
				PreferLocal:       true,  // 優先使用本地 Kubernetes
				LocalContexts: []string{
					"docker-desktop",
					"docker-for-desktop",
					"minikube",
					"kind-",              // kind 集群通常以 kind- 開頭
					"k3s-default",
					"microk8s",
				},
			},
			Database: DatabaseConfig{
				Host:     "localhost",
				Port:     5432,
				User:     "postgres",
				Password: "",
				Database: "postgres",
				SSLMode:  "disable",
			},
			AI: AIConfig{
				DefaultProvider: "claude",
				MaxTokens:       4000,
				Temperature:     0.7,
				SystemPrompt:    "你是 Koopa 的個人開發助手，專門協助軟體開發工作。",
			},
			TaskRunner: TaskRunnerConfig{
				ProjectPaths: []string{".", filepath.Join(os.Getenv("HOME"), "projects")},
				DefaultTool:  "make",
				Environment:  make(map[string]string),
			},
			MCP: MCPConfig{
				ServerEndpoints: []string{},
				RefreshInterval: 30,
			},
		},
		UI: UIConfig{
			Theme:    "cyber",
			DarkMode: true,
			WindowSize: struct {
				Width  int `yaml:"width"`
				Height int `yaml:"height"`
			}{
				Width:  1600,
				Height: 1000,
			},
		},
	}
}

// loadFromFile 從檔案載入配置
func loadFromFile(cfg *Config) error {
	configPaths := []string{
		"configs/config.yaml",
		"config.yaml",
		filepath.Join(os.Getenv("HOME"), ".assistant-go", "config.yaml"),
	}

	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			data, err := os.ReadFile(path)
			if err != nil {
				return fmt.Errorf("failed to read config file %s: %v", path, err)
			}

			if err := yaml.Unmarshal(data, cfg); err != nil {
				return fmt.Errorf("failed to parse config file %s: %v", path, err)
			}

			fmt.Printf("Loaded config from: %s\n", path)
			return nil
		}
	}

	return fmt.Errorf("no config file found in any of the expected locations")
}

// loadFromEnv 從環境變數載入配置
func loadFromEnv(cfg *Config) {
	if val := os.Getenv("ASSISTANT_LOG_LEVEL"); val != "" {
		cfg.App.LogLevel = val
	}
	if val := os.Getenv("ASSISTANT_ENVIRONMENT"); val != "" {
		cfg.App.Environment = val
	}
	if val := os.Getenv("KUBECONFIG"); val != "" {
		cfg.Modules.K8s.KubeConfigPath = val
	}
	if val := os.Getenv("DB_HOST"); val != "" {
		cfg.Modules.Database.Host = val
	}
	if val := os.Getenv("DB_USER"); val != "" {
		cfg.Modules.Database.User = val
	}
	if val := os.Getenv("DB_PASSWORD"); val != "" {
		cfg.Modules.Database.Password = val
	}
	if val := os.Getenv("CLAUDE_API_KEY"); val != "" {
		cfg.Modules.AI.Providers.Claude.APIKey = val
	}
	if val := os.Getenv("GEMINI_API_KEY"); val != "" {
		cfg.Modules.AI.Providers.Gemini.APIKey = val
	}
	if val := os.Getenv("OPENAI_API_KEY"); val != "" {
		cfg.Modules.AI.Providers.OpenAI.APIKey = val
	}
}
