// Package config internal/config/validator.go
package config

import (
	"fmt"
	"os"
	"path/filepath"
)

// validate 驗證配置的有效性
func validate(cfg *Config) error {
	// 驗證應用程式配置
	if err := validateApp(&cfg.App); err != nil {
		return fmt.Errorf("app config validation failed: %v", err)
	}

	// 驗證模組配置
	if err := validateModules(&cfg.Modules); err != nil {
		return fmt.Errorf("modules config validation failed: %v", err)
	}

	// 驗證 UI 配置
	if err := validateUI(&cfg.UI); err != nil {
		return fmt.Errorf("UI config validation failed: %v", err)
	}

	return nil
}

// validateApp 驗證應用程式配置
func validateApp(cfg *AppConfig) error {
	if cfg.Name == "" {
		return fmt.Errorf("app name cannot be empty")
	}

	validLogLevels := map[string]bool{
		"debug": true,
		"info":  true,
		"warn":  true,
		"error": true,
	}

	if !validLogLevels[cfg.LogLevel] {
		return fmt.Errorf("invalid log level: %s", cfg.LogLevel)
	}

	return nil
}

// validateModules 驗證模組配置
func validateModules(cfg *ModulesConfig) error {
	// 驗證 K8s 配置
	if err := validateK8s(&cfg.K8s); err != nil {
		return fmt.Errorf("k8s config validation failed: %v", err)
	}

	// 驗證資料庫配置
	if err := validateDatabase(&cfg.Database); err != nil {
		return fmt.Errorf("database config validation failed: %v", err)
	}

	// 驗證 AI 配置
	if err := validateAI(&cfg.AI); err != nil {
		return fmt.Errorf("AI config validation failed: %v", err)
	}

	// 驗證任務執行器配置
	if err := validateTaskRunner(&cfg.TaskRunner); err != nil {
		return fmt.Errorf("task runner config validation failed: %v", err)
	}

	return nil
}

// validateK8s 驗證 K8s 配置
func validateK8s(cfg *K8sConfig) error {
	if cfg.KubeConfigPath != "" {
		if _, err := os.Stat(cfg.KubeConfigPath); err != nil {
			// kubeconfig 檔案不存在是可以接受的，只是警告
			fmt.Printf("Warning: kubeconfig file not found: %s\n", cfg.KubeConfigPath)
		}
	}

	return nil
}

// validateDatabase 驗證資料庫配置
func validateDatabase(cfg *DatabaseConfig) error {
	if cfg.Port <= 0 || cfg.Port > 65535 {
		return fmt.Errorf("invalid database port: %d", cfg.Port)
	}

	validSSLModes := map[string]bool{
		"disable":     true,
		"require":     true,
		"verify-ca":   true,
		"verify-full": true,
	}

	if !validSSLModes[cfg.SSLMode] {
		return fmt.Errorf("invalid SSL mode: %s", cfg.SSLMode)
	}

	return nil
}

// validateAI 驗證 AI 配置
func validateAI(cfg *AIConfig) error {
	validProviders := map[string]bool{
		"claude": true,
		"gemini": true,
		"openai": true,
	}

	if !validProviders[cfg.DefaultProvider] {
		return fmt.Errorf("invalid AI provider: %s", cfg.DefaultProvider)
	}

	if cfg.MaxTokens <= 0 {
		return fmt.Errorf("max tokens must be positive: %d", cfg.MaxTokens)
	}

	if cfg.Temperature < 0 || cfg.Temperature > 2 {
		return fmt.Errorf("temperature must be between 0 and 2: %f", cfg.Temperature)
	}

	return nil
}

// validateTaskRunner 驗證任務執行器配置
func validateTaskRunner(cfg *TaskRunnerConfig) error {
	validTools := map[string]bool{
		"make": true,
		"task": true,
	}

	if !validTools[cfg.DefaultTool] {
		return fmt.Errorf("invalid default tool: %s", cfg.DefaultTool)
	}

	// 驗證專案路徑
	validPaths := make([]string, 0, len(cfg.ProjectPaths))
	for _, path := range cfg.ProjectPaths {
		// 展開 ~ 符號
		if strings.HasPrefix(path, "~/") {
			homeDir := os.Getenv("HOME")
			if homeDir != "" {
				path = filepath.Join(homeDir, path[2:])
			}
		}

		absPath, err := filepath.Abs(path)
		if err != nil {
			fmt.Printf("Info: Skipping invalid project path: %s (%v)\n", path, err)
			continue
		}

		if _, err := os.Stat(absPath); err != nil {
			fmt.Printf("Info: Project path does not exist, will be created if needed: %s\n", absPath)
		}
		validPaths = append(validPaths, absPath)
	}

	// 更新配置中的路徑為有效路徑
	cfg.ProjectPaths = validPaths

	return nil
}

// validateUI 驗證 UI 配置
func validateUI(cfg *UIConfig) error {
	validThemes := map[string]bool{
		"cyber":    true,
		"material": true,
		"dark":     true,
		"light":    true,
	}

	if !validThemes[cfg.Theme] {
		return fmt.Errorf("invalid theme: %s", cfg.Theme)
	}

	if cfg.WindowSize.Width <= 0 || cfg.WindowSize.Height <= 0 {
		return fmt.Errorf("invalid window size: %dx%d", cfg.WindowSize.Width, cfg.WindowSize.Height)
	}

	return nil
}
