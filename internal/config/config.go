// internal/config/config.go
package config

import (
	"time"
)

// Config 定義應用程式的完整配置結構
type Config struct {
	App      AppConfig      `yaml:"app"`
	Modules  ModulesConfig  `yaml:"modules"`
	UI       UIConfig       `yaml:"ui"`
	Security SecurityConfig `yaml:"security"`
}

// AppConfig 應用程式基本配置
type AppConfig struct {
	Name        string `yaml:"name"`
	Version     string `yaml:"version"`
	LogLevel    string `yaml:"log_level"`
	Environment string `yaml:"environment"`
}

// ModulesConfig 各模組的配置
type ModulesConfig struct {
	K8s        K8sConfig        `yaml:"k8s"`
	Database   DatabaseConfig   `yaml:"database"`
	AI         AIConfig         `yaml:"ai"`
	TaskRunner TaskRunnerConfig `yaml:"task_runner"`
	MCP        MCPConfig        `yaml:"mcp"`
}

// UIConfig UI 相關配置
type UIConfig struct {
	Theme      string `yaml:"theme"`
	WindowSize struct {
		Width  int `yaml:"width"`
		Height int `yaml:"height"`
	} `yaml:"window_size"`
	DarkMode bool `yaml:"dark_mode"`
}

// SecurityConfig 安全相關配置
type SecurityConfig struct {
	EncryptionKey string        `yaml:"encryption_key"`
	TokenExpiry   time.Duration `yaml:"token_expiry"`
}

// K8sConnectionType 定義 Kubernetes 連線類型
type K8sConnectionType string

const (
	K8sConnectionLocal  K8sConnectionType = "local"   // 本地 Kubernetes (Docker Desktop, minikube, kind)
	K8sConnectionGKE    K8sConnectionType = "gke"     // Google Kubernetes Engine
	K8sConnectionCustom K8sConnectionType = "custom"  // 自定義端點
	K8sConnectionAuto   K8sConnectionType = "auto"    // 自動偵測
)

// K8sConfig Kubernetes 配置
type K8sConfig struct {
	// 連線設定
	ConnectionType     K8sConnectionType `yaml:"connection_type"`
	KubeConfigPath     string            `yaml:"kubeconfig_path"`
	CurrentContext     string            `yaml:"current_context"`
	Namespace          string            `yaml:"namespace"`

	// 自定義連線設定
	CustomEndpoint     string            `yaml:"custom_endpoint"`
	CustomToken        string            `yaml:"custom_token"`
	CustomCertPath     string            `yaml:"custom_cert_path"`

	// 連線選項
	AutoRetry          bool              `yaml:"auto_retry"`
	RetryInterval      int               `yaml:"retry_interval"` // 秒
	ConnectionTimeout  int               `yaml:"connection_timeout"` // 秒

	// 本地 Kubernetes 偵測
	PreferLocal        bool              `yaml:"prefer_local"`
	LocalContexts      []string          `yaml:"local_contexts"` // 本地上下文名稱列表
}

// DatabaseConfig 資料庫配置
type DatabaseConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	Database string `yaml:"database"`
	SSLMode  string `yaml:"ssl_mode"`
}

// AIConfig AI 助理配置
type AIConfig struct {
	DefaultProvider string  `yaml:"default_provider"`
	MaxTokens       int     `yaml:"max_tokens"`
	Temperature     float64 `yaml:"temperature"`
	SystemPrompt    string  `yaml:"system_prompt"`
	Providers       struct {
		Claude struct {
			APIKey string `yaml:"api_key"`
			Model  string `yaml:"model"`
		} `yaml:"claude"`
		Gemini struct {
			APIKey string `yaml:"api_key"`
			Model  string `yaml:"model"`
		} `yaml:"gemini"`
		OpenAI struct {
			APIKey string `yaml:"api_key"`
			Model  string `yaml:"model"`
		} `yaml:"openai"`
	} `yaml:"providers"`
}

// TaskRunnerConfig 任務執行器配置
type TaskRunnerConfig struct {
	ProjectPaths []string          `yaml:"project_paths"`
	DefaultTool  string            `yaml:"default_tool"`
	Environment  map[string]string `yaml:"environment"`
}

// MCPConfig MCP 視覺化配置
type MCPConfig struct {
	ServerEndpoints []string `yaml:"server_endpoints"`
	RefreshInterval int      `yaml:"refresh_interval"`
}
