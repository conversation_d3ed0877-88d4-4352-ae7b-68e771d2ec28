// Package k8s internal/modules/k8s/auth.go
package k8s

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/koopa0/assistant-go/internal/config"
	"github.com/koopa0/assistant-go/internal/utils"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/transport"
)

// AuthManager 管理 Kubernetes 認證
type AuthManager struct {
	config *config.K8sConfig
	logger *utils.Logger
}

// NewAuthManager 建立新的認證管理器
func NewAuthManager(cfg *config.K8sConfig, logger *utils.Logger) *AuthManager {
	return &AuthManager{
		config: cfg,
		logger: logger,
	}
}

// CreateModernClient 使用現代認證方式建立 Kubernetes 客戶端
func (am *AuthManager) CreateModernClient(contextName string) (*kubernetes.Clientset, error) {
	// 建立基本配置
	restConfig, err := am.buildRestConfig(contextName)
	if err != nil {
		return nil, fmt.Errorf("failed to build rest config: %v", err)
	}

	// 配置現代認證
	if err := am.configureModernAuth(restConfig); err != nil {
		am.logger.Warn("Failed to configure modern auth, falling back to default: %v", err)
	}

	// 設定超時和重試
	am.configureClientOptions(restConfig)

	// 建立客戶端
	clientset, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create kubernetes client: %v", err)
	}

	return clientset, nil
}

// buildRestConfig 建立基本的 REST 配置
func (am *AuthManager) buildRestConfig(contextName string) (*rest.Config, error) {
	var restConfig *rest.Config
	var err error

	if contextName == "" {
		// 使用預設配置
		restConfig, err = clientcmd.BuildConfigFromFlags("", am.config.KubeConfigPath)
	} else {
		// 使用指定上下文
		overrides := &clientcmd.ConfigOverrides{
			CurrentContext: contextName,
		}
		restConfig, err = clientcmd.NewNonInteractiveDeferredLoadingClientConfig(
			&clientcmd.ClientConfigLoadingRules{ExplicitPath: am.config.KubeConfigPath},
			overrides,
		).ClientConfig()
	}

	if err != nil {
		return nil, err
	}

	return restConfig, nil
}

// configureModernAuth 配置現代認證方式
func (am *AuthManager) configureModernAuth(restConfig *rest.Config) error {
	// 檢查是否已經有有效的認證配置
	if restConfig.BearerToken != "" || restConfig.AuthProvider != nil {
		am.logger.Debug("Using existing authentication configuration")
		return nil
	}

	// 如果有自定義 token，使用它
	if am.config.CustomToken != "" {
		restConfig.BearerToken = am.config.CustomToken
		am.logger.Debug("Using custom bearer token")
		return nil
	}

	// 嘗試使用 TokenRequest API
	if err := am.configureTokenRequestAuth(restConfig); err != nil {
		am.logger.Debug("TokenRequest API not available: %v", err)
		// 繼續使用預設認證
	}

	return nil
}

// configureTokenRequestAuth 配置 TokenRequest API 認證
func (am *AuthManager) configureTokenRequestAuth(restConfig *rest.Config) error {
	// 這裡可以實作 TokenRequest API 的邏輯
	// 目前先跳過，因為需要更複雜的實作
	am.logger.Debug("TokenRequest API configuration skipped for now")
	return nil
}

// configureClientOptions 配置客戶端選項
func (am *AuthManager) configureClientOptions(restConfig *rest.Config) {
	// 設定超時
	restConfig.Timeout = time.Duration(am.config.ConnectionTimeout) * time.Second

	// 設定 QPS 和 Burst 以提高性能
	restConfig.QPS = 50
	restConfig.Burst = 100

	// 配置 Transport 以減少警告
	if restConfig.Transport == nil {
		restConfig.Transport = &http.Transport{
			TLSHandshakeTimeout:   10 * time.Second,
			ResponseHeaderTimeout: 10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
		}
	}

	// 配置 Warning Handler 來處理 Kubernetes 警告
	restConfig.WarningHandler = am.createWarningHandler()

	am.logger.Debug("Configured client options: timeout=%v, QPS=%.1f, Burst=%d",
		restConfig.Timeout, restConfig.QPS, restConfig.Burst)
}

// createWarningHandler 建立警告處理器
func (am *AuthManager) createWarningHandler() rest.WarningHandler {
	return rest.WarningHandlerFunc(func(warning rest.WarningHeader) {
		warningText := warning.Text
		
		// 過濾掉已知的 token 相關警告，避免重複顯示
		if am.shouldSuppressWarning(warningText) {
			am.logger.Debug("Suppressed K8s warning: %s", warningText)
			return
		}

		// 顯示其他重要警告
		am.logger.Warn("Kubernetes warning: %s", warningText)
	})
}

// shouldSuppressWarning 判斷是否應該抑制警告
func (am *AuthManager) shouldSuppressWarning(warning string) bool {
	suppressedWarnings := []string{
		"Use tokens from the TokenRequest API",
		"auto-generated secret-based tokens",
		"manually created secret-based tokens",
	}

	for _, suppressed := range suppressedWarnings {
		if contains(warning, suppressed) {
			return true
		}
	}

	return false
}

// TestConnection 測試連線並返回詳細資訊
func (am *AuthManager) TestConnection(contextName string) (*ConnectionTestResult, error) {
	result := &ConnectionTestResult{
		Context:   contextName,
		StartTime: time.Now(),
	}

	// 建立客戶端
	client, err := am.CreateModernClient(contextName)
	if err != nil {
		result.Error = err
		result.Duration = time.Since(result.StartTime)
		return result, err
	}

	// 測試連線
	ctx, cancel := context.WithTimeout(context.Background(), 
		time.Duration(am.config.ConnectionTimeout)*time.Second)
	defer cancel()

	version, err := client.Discovery().ServerVersion()
	result.Duration = time.Since(result.StartTime)

	if err != nil {
		result.Error = err
		return result, err
	}

	result.Success = true
	result.ServerVersion = version.String()
	result.GitVersion = version.GitVersion

	am.logger.Info("Successfully connected to K8s context '%s' (version: %s) in %v",
		contextName, version.GitVersion, result.Duration)

	return result, nil
}

// ConnectionTestResult 連線測試結果
type ConnectionTestResult struct {
	Context       string
	Success       bool
	ServerVersion string
	GitVersion    string
	StartTime     time.Time
	Duration      time.Duration
	Error         error
}

// 輔助函數
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    (len(s) > len(substr) && 
		     (s[:len(substr)] == substr || 
		      s[len(s)-len(substr):] == substr || 
		      containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
