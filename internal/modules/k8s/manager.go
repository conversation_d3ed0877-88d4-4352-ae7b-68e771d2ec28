// Package k8s internal/modules/k8s/manager.go
package k8s

import (
	"context"
	"fmt"
	"sync"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"

	"github.com/koopa0/assistant-go/internal/config"
	"github.com/koopa0/assistant-go/internal/modules"
	"github.com/koopa0/assistant-go/internal/utils"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

// Manager Kubernetes 資源管理器
type Manager struct {
	config           *config.K8sConfig
	client           *kubernetes.Clientset
	namespace        binding.String
	mutex            sync.RWMutex
	logger           *utils.Logger
	connectionMgr    *ConnectionManager
	currentConnection *ConnectionInfo
	monitoringActive bool
	stopMonitoring   chan bool

	// UI 元件
	ui *UI

	// 資料
	pods     []v1.Pod
	selected int
}

// NewManager 建立新的 K8s 管理器
func NewManager(cfg *config.K8sConfig, logger *utils.Logger) *Manager {
	return &Manager{
		config:         cfg,
		namespace:      binding.NewString(),
		selected:       -1,
		logger:         logger,
		connectionMgr:  NewConnectionManager(cfg, logger),
		stopMonitoring: make(chan bool, 1),
	}
}

// Initialize 實作 Module 介面
func (m *Manager) Initialize(ctx context.Context) error {
	m.logger.Info("Initializing K8s module...")

	// 嘗試建立最佳連線
	if err := m.connectToBestAvailable(); err != nil {
		m.logger.Warn("Failed to establish K8s connection: %v", err)
		// 不返回錯誤，允許模組載入但顯示未連接狀態
	}

	// 設定初始命名空間
	m.namespace.Set(m.config.Namespace)

	// 建立 UI
	m.ui = NewUI(m)

	// 根據配置決定是否開始自動監控
	if m.config.AutoRetry && m.IsConnected() {
		go m.startMonitoring()
	}

	m.logger.Info("K8s module initialized successfully")
	return nil
}

// Content 實作 Module 介面
func (m *Manager) Content() fyne.CanvasObject {
	if m.ui == nil {
		return container.NewVBox()
	}
	return m.ui.Content()
}

// Refresh 實作 Module 介面
func (m *Manager) Refresh() error {
	m.logger.Info("Refreshing K8s module...")

	if m.ui != nil {
		m.ui.Refresh()
	}

	m.refreshPods()
	return nil
}

// Shutdown 實作 Module 介面
func (m *Manager) Shutdown() error {
	m.logger.Info("Shutting down K8s module...")

	// 停止監控
	m.stopMonitoringProcess()

	m.logger.Info("K8s module shut down successfully")
	return nil
}

// Info 實作 Module 介面
func (m *Manager) Info() modules.ModuleInfo {
	return modules.ModuleInfo{
		ID:          "k8s",
		Name:        "Kubernetes Navigator",
		Description: "Kubernetes cluster management and monitoring",
		Version:     "1.0.0",
		Author:      "Koopa",
	}
}

// connectToBestAvailable 連線到最佳可用的 Kubernetes 集群
func (m *Manager) connectToBestAvailable() error {
	bestConn := m.connectionMgr.GetBestConnection()
	if bestConn == nil {
		return utils.NewError(utils.ErrCodeNetwork, "no Kubernetes connections available")
	}

	return m.connectToSpecific(bestConn.Context)
}

// connectToSpecific 連線到指定的 Kubernetes 上下文
func (m *Manager) connectToSpecific(contextName string) error {
	m.logger.Info("Attempting to connect to K8s context: %s", contextName)

	// 建立客戶端
	clientset, err := m.connectionMgr.CreateClientForConnection(contextName)
	if err != nil {
		return utils.WrapError(err, utils.ErrCodeNetwork, "failed to create K8s client for context: "+contextName)
	}

	// 更新連線資訊
	connections := m.connectionMgr.DetectAvailableConnections()
	for _, conn := range connections {
		if conn.Context == contextName {
			m.mutex.Lock()
			m.client = clientset
			m.currentConnection = &conn
			m.mutex.Unlock()

			m.logger.Info("Successfully connected to K8s context: %s (%s)", contextName, conn.Description)
			return nil
		}
	}

	m.mutex.Lock()
	m.client = clientset
	m.currentConnection = &ConnectionInfo{
		Context:     contextName,
		Type:        config.K8sConnectionCustom,
		Available:   true,
		Description: "Connected Context",
	}
	m.mutex.Unlock()

	m.logger.Info("Successfully connected to K8s context: %s", contextName)
	return nil
}

// refreshPods 重新載入 Pod 列表
func (m *Manager) refreshPods() {
	if m.client == nil {
		return
	}

	ns, _ := m.namespace.Get()
	pods, err := m.client.CoreV1().Pods(ns).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		m.logger.Warn("Failed to list pods: %v", err)
		return
	}

	m.mutex.Lock()
	m.pods = pods.Items
	m.mutex.Unlock()

	if m.ui != nil {
		m.ui.RefreshPods()
	}

	m.logger.Debug("Refreshed %d pods in namespace %s", len(pods.Items), ns)
}

// GetPods 取得 Pod 列表
func (m *Manager) GetPods() []v1.Pod {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	pods := make([]v1.Pod, len(m.pods))
	copy(pods, m.pods)
	return pods
}

// GetPodLogs 獲取 Pod 日誌
func (m *Manager) GetPodLogs(podName, namespace string) string {
	if m.client == nil {
		return "無法連接到 Kubernetes"
	}

	podLogOptions := &v1.PodLogOptions{
		TailLines: int64Ptr(100),
	}

	req := m.client.CoreV1().Pods(namespace).GetLogs(podName, podLogOptions)
	logs, err := req.Stream(context.TODO())
	if err != nil {
		return fmt.Sprintf("無法獲取日誌: %v", err)
	}
	defer logs.Close()

	buf := make([]byte, 2048)
	n, _ := logs.Read(buf)
	return string(buf[:n])
}

// SelectPod 選擇 Pod
func (m *Manager) SelectPod(index int) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if index >= 0 && index < len(m.pods) {
		m.selected = index
		if m.ui != nil {
			m.ui.ShowPodDetails(m.pods[index])
		}
	}
}

// startMonitoring 開始監控 K8s 資源
func (m *Manager) startMonitoring() {
	if m.monitoringActive {
		return
	}

	m.monitoringActive = true
	interval := time.Duration(m.config.RetryInterval) * time.Second
	if interval < 5*time.Second {
		interval = 5 * time.Second // 最小間隔 5 秒
	}

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	m.logger.Info("Started K8s monitoring with %v interval", interval)

	for {
		select {
		case <-ticker.C:
			if m.client != nil {
				m.refreshPods()
			} else if m.config.AutoRetry {
				// 嘗試重新連線
				if err := m.connectToBestAvailable(); err != nil {
					m.logger.Debug("Auto-retry connection failed: %v", err)
				} else {
					m.logger.Info("Auto-retry connection successful")
				}
			}
		case <-m.stopMonitoring:
			m.logger.Info("Stopped K8s monitoring")
			m.monitoringActive = false
			return
		}
	}
}

// stopMonitoringProcess 停止監控程序
func (m *Manager) stopMonitoringProcess() {
	if m.monitoringActive {
		select {
		case m.stopMonitoring <- true:
		default:
		}
	}
}

// IsConnected 檢查是否已連接
func (m *Manager) IsConnected() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.client != nil
}

// UpdateConfig 更新配置並重新連接
func (m *Manager) UpdateConfig(cfg *config.K8sConfig) error {
	m.config = cfg
	m.connectionMgr = NewConnectionManager(cfg, m.logger)
	return m.connectToBestAvailable()
}

// GetAvailableConnections 獲取可用連線列表
func (m *Manager) GetAvailableConnections() []ConnectionInfo {
	return m.connectionMgr.DetectAvailableConnections()
}

// GetCurrentConnection 獲取當前連線資訊
func (m *Manager) GetCurrentConnection() *ConnectionInfo {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.currentConnection
}

// SwitchConnection 切換到指定連線
func (m *Manager) SwitchConnection(contextName string) error {
	// 停止當前監控
	m.stopMonitoringProcess()

	// 連線到新的上下文
	if err := m.connectToSpecific(contextName); err != nil {
		return err
	}

	// 重新開始監控（如果配置允許）
	if m.config.AutoRetry && m.IsConnected() {
		go m.startMonitoring()
	}

	// 重新整理 UI
	if m.ui != nil {
		m.ui.Refresh()
	}

	return nil
}

// StartManualMonitoring 手動開始監控
func (m *Manager) StartManualMonitoring() {
	if !m.monitoringActive && m.IsConnected() {
		go m.startMonitoring()
	}
}

// StopManualMonitoring 手動停止監控
func (m *Manager) StopManualMonitoring() {
	m.stopMonitoringProcess()
}

// TestConnection 測試指定連線
func (m *Manager) TestConnection(contextName string) error {
	connections := m.connectionMgr.DetectAvailableConnections()
	for _, conn := range connections {
		if conn.Context == contextName {
			if conn.Available {
				return nil
			}
			return conn.Error
		}
	}
	return utils.NewError(utils.ErrCodeNotFound, "connection context not found: "+contextName)
}

// NewK8sManager 建立新的 K8s 管理器 (為了兼容性)
func NewK8sManager(cfg *config.K8sConfig, logger *utils.Logger) *Manager {
	return NewManager(cfg, logger)
}

// 輔助函數
func int64Ptr(i int64) *int64 {
	return &i
}
