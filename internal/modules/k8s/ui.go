// Package k8s internal/modules/k8s/ui.go
package k8s

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"

	v1 "k8s.io/api/core/v1"
)

// UI K8s 模組的使用者介面
type UI struct {
	manager *Manager
	content fyne.CanvasObject

	// UI 元件
	podTable         *widget.Table
	resourceChart    *ResourceChart
	quotaDisplay     *QuotaDisplay
	logViewer        *widget.Entry
	statusLabel      *widget.Label

	// 連線設定 UI 元件
	connectionSelect *widget.Select
	connectionStatus *widget.Label
	connectButton    *widget.Button
	refreshConnBtn   *widget.Button
	testConnBtn      *widget.Button
	monitoringBtn    *widget.Button
	connectionPanel  *fyne.Container

	// 狀態管理
	isConnected      bool
}

// NewUI 建立新的 K8s UI
func NewUI(manager *Manager) *UI {
	ui := &UI{
		manager: manager,
	}
	ui.buildUI()
	return ui
}

// Content 返回 UI 內容
func (ui *UI) Content() fyne.CanvasObject {
	return ui.content
}

// buildUI 建立使用者介面
func (ui *UI) buildUI() {
	// 建立連線設定面板
	ui.createConnectionPanel()

	// 建立命名空間選擇器
	nsSelector := ui.createNamespaceSelector()

	// 建立 Pod 表格
	ui.createPodTable()

	// 建立資源使用圖表
	ui.resourceChart = NewResourceChart()

	// 建立配額顯示
	ui.quotaDisplay = NewQuotaDisplay()

	// 建立日誌檢視器
	ui.logViewer = widget.NewMultiLineEntry()
	ui.logViewer.SetPlaceHolder("選擇一個 Pod 來查看日誌...")

	// 左側面板：連線設定 + 命名空間 + Pod 表格
	leftPanel := container.NewBorder(
		container.NewVBox(ui.connectionPanel, widget.NewSeparator(), nsSelector),
		nil,
		nil,
		nil,
		container.NewScroll(ui.podTable), // Pod 表格添加滾動
	)

	// 右側面板：資源監控 + 日誌
	rightPanel := container.NewVSplit(
		container.NewHSplit(
			container.NewScroll(ui.createCard("資源使用情況", ui.resourceChart)), // 資源圖表添加滾動
			container.NewScroll(ui.createCard("配額限制", ui.quotaDisplay)),    // 配額顯示添加滾動
		),
		ui.createCard("Pod 日誌", container.NewScroll(ui.logViewer)), // 日誌已有滾動
	)
	rightPanel.SetOffset(0.4)

	// 組合主要內容
	ui.content = container.NewHSplit(
		container.NewScroll(leftPanel),  // 左側面板添加滾動
		container.NewScroll(rightPanel), // 右側面板添加滾動
	)
}

// createConnectionPanel 建立連線設定面板
func (ui *UI) createConnectionPanel() {
	// 連線狀態顯示
	ui.connectionStatus = widget.NewLabelWithStyle("❌ 未連接", fyne.TextAlignCenter, fyne.TextStyle{Bold: true, Monospace: true})
	ui.updateConnectionStatus()

	// 連線選擇下拉選單
	ui.connectionSelect = widget.NewSelect([]string{}, func(selected string) {
		// 選擇變更時不自動連線，等待用戶點擊連線按鈕
	})
	ui.connectionSelect.PlaceHolder = "選擇 Kubernetes 連線..."

	// 連線按鈕
	ui.connectButton = widget.NewButtonWithIcon("連線", theme.ConfirmIcon(), func() {
		ui.connectToSelected()
	})

	// 重新整理連線列表按鈕
	ui.refreshConnBtn = widget.NewButtonWithIcon("重新整理", theme.ViewRefreshIcon(), func() {
		ui.refreshConnectionList()
	})

	// 測試連線按鈕
	ui.testConnBtn = widget.NewButtonWithIcon("測試", theme.InfoIcon(), func() {
		ui.testSelectedConnection()
	})

	// 監控控制按鈕
	ui.monitoringBtn = widget.NewButtonWithIcon("開始監控", theme.MediaPlayIcon(), func() {
		ui.toggleMonitoring()
	})

	// 初始化連線列表
	ui.refreshConnectionList()

	// 組合連線控制面板
	connectionControls := container.NewGridWithColumns(2,
		ui.connectButton,
		ui.refreshConnBtn,
		ui.testConnBtn,
		ui.monitoringBtn,
	)

	ui.connectionPanel = container.NewVBox(
		widget.NewLabelWithStyle("🔗 KUBERNETES CONNECTION", fyne.TextAlignCenter, fyne.TextStyle{Bold: true, Monospace: true}),
		ui.connectionStatus,
		widget.NewSeparator(),
		widget.NewLabel("選擇連線:"),
		ui.connectionSelect,
		connectionControls,
	)
}

// createNamespaceSelector 建立命名空間選擇器
func (ui *UI) createNamespaceSelector() *fyne.Container {
	// 連線狀態指示器
	ui.statusLabel = widget.NewLabel("❌ 未連接")
	if ui.manager.IsConnected() {
		ui.statusLabel.SetText("✅ 已連接")
	}

	// 命名空間輸入
	nsEntry := widget.NewEntryWithData(ui.manager.namespace)
	nsEntry.SetPlaceHolder("輸入命名空間...")

	// 重新載入按鈕
	refreshBtn := widget.NewButtonWithIcon("重新整理", theme.ViewRefreshIcon(), func() {
		ui.manager.refreshPods()
	})

	// 快速切換按鈕
	defaultBtn := widget.NewButton("default", func() {
		ui.manager.namespace.Set("default")
		ui.manager.refreshPods()
	})

	kubeSystemBtn := widget.NewButton("kube-system", func() {
		ui.manager.namespace.Set("kube-system")
		ui.manager.refreshPods()
	})

	return container.NewVBox(
		widget.NewLabelWithStyle("🚢 KUBERNETES CONTROL", fyne.TextAlignCenter, fyne.TextStyle{Bold: true, Monospace: true}),
		ui.statusLabel,
		widget.NewSeparator(),
		widget.NewLabel("命名空間:"),
		nsEntry,
		container.NewHBox(refreshBtn, defaultBtn, kubeSystemBtn),
	)
}

// createPodTable 建立 Pod 表格
func (ui *UI) createPodTable() {
	ui.podTable = widget.NewTable(
		func() (int, int) {
			return len(ui.manager.GetPods()), 5 // 5 個欄位：名稱,狀態,重啟次數,CPU,記憶體
		},
		func() fyne.CanvasObject {
			return widget.NewLabel("載入中...")
		},
		func(id widget.TableCellID, cell fyne.CanvasObject) {
			label := cell.(*widget.Label)
			pods := ui.manager.GetPods()
			if id.Row >= len(pods) {
				return
			}

			pod := pods[id.Row]
			switch id.Col {
			case 0:
				label.SetText(pod.Name)
			case 1:
				label.SetText(string(pod.Status.Phase))
				// 根據狀態設定顏色
				switch pod.Status.Phase {
				case v1.PodRunning:
					label.TextStyle = fyne.TextStyle{Bold: true}
				case v1.PodFailed:
					label.TextStyle = fyne.TextStyle{Bold: true}
				}
			case 2:
				restarts := 0
				for _, cs := range pod.Status.ContainerStatuses {
					restarts += int(cs.RestartCount)
				}
				label.SetText(fmt.Sprintf("%d", restarts))
			case 3:
				// CPU 使用量 (需要 metrics API)
				label.SetText("--")
			case 4:
				// 記憶體使用量 (需要 metrics API)
				label.SetText("--")
			}
		},
	)

	// 設定列選擇
	ui.podTable.OnSelected = func(id widget.TableCellID) {
		ui.manager.SelectPod(id.Row)
	}

	// 設定標題列
	ui.podTable.ShowHeaderRow = true
	ui.podTable.CreateHeader = func() fyne.CanvasObject {
		return container.NewHBox(
			widget.NewLabelWithStyle("名稱", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
			widget.NewLabelWithStyle("狀態", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
			widget.NewLabelWithStyle("重啟", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
			widget.NewLabelWithStyle("CPU", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
			widget.NewLabelWithStyle("記憶體", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		)
	}
}

// ShowPodDetails 顯示 Pod 詳細資訊
func (ui *UI) ShowPodDetails(pod v1.Pod) {
	// 獲取 Pod 日誌
	go func() {
		logs := ui.manager.GetPodLogs(pod.Name, pod.Namespace)
		ui.logViewer.SetText(logs)
	}()

	// 更新資源使用情況
	ui.updatePodResources(pod)
}

// updatePodResources 更新單個 Pod 的資源資訊
func (ui *UI) updatePodResources(pod v1.Pod) {
	// 更新配額顯示
	ui.quotaDisplay.UpdatePod(pod)
}

// createCard 建立帶標題的卡片容器
func (ui *UI) createCard(title string, content fyne.CanvasObject) *fyne.Container {
	titleLabel := widget.NewLabelWithStyle(title, fyne.TextAlignLeading, fyne.TextStyle{Bold: true})
	return container.NewBorder(titleLabel, nil, nil, nil, content)
}

// RefreshPods 重新整理 Pod 表格
func (ui *UI) RefreshPods() {
	if ui.podTable != nil {
		ui.podTable.Refresh()
	}

	// 更新資源圖表
	ui.updateResourceChart()
}

// updateResourceChart 更新資源使用圖表
func (ui *UI) updateResourceChart() {
	// 這裡應該使用 metrics API 來獲取實際的資源使用情況
	// 目前使用模擬資料
	ui.resourceChart.UpdateData(ui.manager.GetPods())
}

// Refresh 重新整理整個 UI
func (ui *UI) Refresh() {
	ui.RefreshPods()

	// 更新連線狀態
	if ui.statusLabel != nil {
		if ui.manager.IsConnected() {
			ui.statusLabel.SetText("✅ 已連接")
		} else {
			ui.statusLabel.SetText("❌ 未連接")
		}
	}

	// 更新連線面板狀態
	ui.updateConnectionStatus()

	// 更新監控按鈕狀態
	if ui.monitoringBtn != nil {
		if ui.manager.monitoringActive {
			ui.monitoringBtn.SetText("停止監控")
			ui.monitoringBtn.SetIcon(theme.MediaPauseIcon())
		} else {
			ui.monitoringBtn.SetText("開始監控")
			ui.monitoringBtn.SetIcon(theme.MediaPlayIcon())
		}
	}
}

// ResourceChart 資源使用圖表元件
type ResourceChart struct {
	widget.BaseWidget
	container *fyne.Container
}

func NewResourceChart() *ResourceChart {
	r := &ResourceChart{}
	r.ExtendBaseWidget(r)

	// 建立圖表 UI
	r.container = container.NewVBox(
		widget.NewLabel("CPU 使用率"),
		widget.NewProgressBar(),
		widget.NewLabel("記憶體使用率"),
		widget.NewProgressBar(),
		widget.NewLabel("網路 I/O"),
		widget.NewProgressBar(),
	)

	return r
}

func (r *ResourceChart) CreateRenderer() fyne.WidgetRenderer {
	return widget.NewSimpleRenderer(r.container)
}

func (r *ResourceChart) UpdateData(pods []v1.Pod) {
	// 更新圖表資料
	// TODO: 實作真實的資源監控
}

// QuotaDisplay 配額顯示元件
type QuotaDisplay struct {
	widget.BaseWidget
	container *fyne.Container
}

func NewQuotaDisplay() *QuotaDisplay {
	q := &QuotaDisplay{}
	q.ExtendBaseWidget(q)

	// 建立配額顯示 UI
	q.container = container.NewVBox(
		widget.NewLabel("CPU 限制: --"),
		widget.NewLabel("記憶體限制: --"),
		widget.NewLabel("儲存限制: --"),
		widget.NewLabel("Pod 數量: --"),
	)

	return q
}

func (q *QuotaDisplay) CreateRenderer() fyne.WidgetRenderer {
	return widget.NewSimpleRenderer(q.container)
}

func (q *QuotaDisplay) UpdatePod(pod v1.Pod) {
	// 更新配額資訊
	// TODO: 實作真實的配額監控
}

// 連線管理方法

// updateConnectionStatus 更新連線狀態顯示
func (ui *UI) updateConnectionStatus() {
	if ui.connectionStatus == nil {
		return
	}

	currentConn := ui.manager.GetCurrentConnection()
	if currentConn != nil && currentConn.Available {
		statusText := fmt.Sprintf("✅ 已連接: %s", currentConn.Description)
		if currentConn.IsLocal {
			statusText += " (本地)"
		}
		ui.connectionStatus.SetText(statusText)
	} else if currentConn != nil {
		ui.connectionStatus.SetText(fmt.Sprintf("⚠️ 連線異常: %s", currentConn.Context))
	} else {
		ui.connectionStatus.SetText("❌ 未連接")
	}
}

// refreshConnectionList 重新整理連線列表
func (ui *UI) refreshConnectionList() {
	connections := ui.manager.GetAvailableConnections()
	var options []string
	var optionMap = make(map[string]ConnectionInfo)

	for _, conn := range connections {
		var label string
		if conn.Available {
			if conn.IsLocal {
				label = fmt.Sprintf("✅ %s (本地)", conn.Description)
			} else {
				label = fmt.Sprintf("✅ %s", conn.Description)
			}
		} else {
			if conn.IsLocal {
				label = fmt.Sprintf("❌ %s (本地) - %v", conn.Description, conn.Error)
			} else {
				label = fmt.Sprintf("❌ %s - %v", conn.Description, conn.Error)
			}
		}

		options = append(options, label)
		optionMap[label] = conn
	}

	if len(options) == 0 {
		options = []string{"❌ 未找到 Kubernetes 連線"}
	}

	ui.connectionSelect.Options = options
	ui.connectionSelect.Refresh()

	// 如果當前有連線，選中它
	if currentConn := ui.manager.GetCurrentConnection(); currentConn != nil {
		for label, conn := range optionMap {
			if conn.Context == currentConn.Context {
				ui.connectionSelect.SetSelected(label)
				break
			}
		}
	}
}

// connectToSelected 連線到選中的 Kubernetes 集群
func (ui *UI) connectToSelected() {
	selected := ui.connectionSelect.Selected
	if selected == "" {
		return
	}

	// 從選項中提取上下文名稱
	connections := ui.manager.GetAvailableConnections()
	for _, conn := range connections {
		var label string
		if conn.Available {
			if conn.IsLocal {
				label = fmt.Sprintf("✅ %s (本地)", conn.Description)
			} else {
				label = fmt.Sprintf("✅ %s", conn.Description)
			}
		} else {
			if conn.IsLocal {
				label = fmt.Sprintf("❌ %s (本地) - %v", conn.Description, conn.Error)
			} else {
				label = fmt.Sprintf("❌ %s - %v", conn.Description, conn.Error)
			}
		}

		if label == selected {
			ui.connectButton.SetText("連線中...")
			ui.connectButton.Disable()

			go func() {
				defer func() {
					ui.connectButton.SetText("連線")
					ui.connectButton.Enable()
				}()

				if err := ui.manager.SwitchConnection(conn.Context); err != nil {
					ui.manager.logger.Error("Failed to switch connection: %v", err)
				}

				// 更新 UI
				ui.updateConnectionStatus()
				ui.Refresh()
			}()
			break
		}
	}
}

// testSelectedConnection 測試選中的連線
func (ui *UI) testSelectedConnection() {
	selected := ui.connectionSelect.Selected
	if selected == "" {
		return
	}

	connections := ui.manager.GetAvailableConnections()
	for _, conn := range connections {
		var label string
		if conn.Available {
			if conn.IsLocal {
				label = fmt.Sprintf("✅ %s (本地)", conn.Description)
			} else {
				label = fmt.Sprintf("✅ %s", conn.Description)
			}
		} else {
			if conn.IsLocal {
				label = fmt.Sprintf("❌ %s (本地) - %v", conn.Description, conn.Error)
			} else {
				label = fmt.Sprintf("❌ %s - %v", conn.Description, conn.Error)
			}
		}

		if label == selected {
			ui.testConnBtn.SetText("測試中...")
			ui.testConnBtn.Disable()

			go func() {
				defer func() {
					ui.testConnBtn.SetText("測試")
					ui.testConnBtn.Enable()
				}()

				if err := ui.manager.TestConnection(conn.Context); err != nil {
					ui.manager.logger.Error("Connection test failed: %v", err)
				} else {
					ui.manager.logger.Info("Connection test successful for: %s", conn.Context)
				}

				// 重新整理連線列表以更新狀態
				ui.refreshConnectionList()
			}()
			break
		}
	}
}

// toggleMonitoring 切換監控狀態
func (ui *UI) toggleMonitoring() {
	if ui.manager.monitoringActive {
		ui.manager.StopManualMonitoring()
		ui.monitoringBtn.SetText("開始監控")
		ui.monitoringBtn.SetIcon(theme.MediaPlayIcon())
	} else {
		ui.manager.StartManualMonitoring()
		ui.monitoringBtn.SetText("停止監控")
		ui.monitoringBtn.SetIcon(theme.MediaPauseIcon())
	}
}
