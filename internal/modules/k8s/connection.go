// Package k8s internal/modules/k8s/connection.go
package k8s

import (
	"context"
	"os"
	"strings"
	"time"

	"github.com/koopa0/assistant-go/internal/config"
	"github.com/koopa0/assistant-go/internal/utils"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

// ConnectionManager 管理 Kubernetes 連線
type ConnectionManager struct {
	config *config.K8sConfig
	logger *utils.Logger
}

// ConnectionInfo 連線資訊
type ConnectionInfo struct {
	Type        config.K8sConnectionType
	Context     string
	Endpoint    string
	Namespace   string
	IsLocal     bool
	Available   bool
	Error       error
	Description string
}

// NewConnectionManager 建立新的連線管理器
func NewConnectionManager(cfg *config.K8sConfig, logger *utils.Logger) *ConnectionManager {
	return &ConnectionManager{
		config: cfg,
		logger: logger,
	}
}

// DetectAvailableConnections 偵測可用的 Kubernetes 連線
func (cm *ConnectionManager) DetectAvailableConnections() []ConnectionInfo {
	var connections []ConnectionInfo

	// 檢查 kubeconfig 是否存在
	if _, err := os.Stat(cm.config.KubeConfigPath); err != nil {
		cm.logger.Warn("Kubeconfig file not found: %s", cm.config.KubeConfigPath)
		return connections
	}

	// 載入 kubeconfig
	config, err := clientcmd.LoadFromFile(cm.config.KubeConfigPath)
	if err != nil {
		cm.logger.Error("Failed to load kubeconfig: %v", err)
		return connections
	}

	// 檢查每個上下文
	for contextName, context := range config.Contexts {
		cluster := config.Clusters[context.Cluster]
		if cluster == nil {
			continue
		}

		connInfo := ConnectionInfo{
			Context:   contextName,
			Endpoint:  cluster.Server,
			Namespace: context.Namespace,
			Available: false,
		}

		// 判斷連線類型
		connInfo.Type, connInfo.IsLocal = cm.determineConnectionType(contextName, cluster.Server)
		connInfo.Description = cm.getConnectionDescription(connInfo.Type, contextName)

		// 測試連線可用性
		connInfo.Available, connInfo.Error = cm.testConnection(contextName)

		connections = append(connections, connInfo)
	}

	// 按優先級排序：本地優先，然後是可用的連線
	return cm.sortConnectionsByPriority(connections)
}

// determineConnectionType 判斷連線類型
func (cm *ConnectionManager) determineConnectionType(contextName, endpoint string) (config.K8sConnectionType, bool) {
	contextLower := strings.ToLower(contextName)
	endpointLower := strings.ToLower(endpoint)

	// 檢查是否為本地連線
	for _, localContext := range cm.config.LocalContexts {
		if strings.Contains(contextLower, strings.ToLower(localContext)) {
			return config.K8sConnectionLocal, true
		}
	}

	// 檢查端點是否為本地
	if strings.Contains(endpointLower, "localhost") ||
		strings.Contains(endpointLower, "127.0.0.1") ||
		strings.Contains(endpointLower, "docker.internal") {
		return config.K8sConnectionLocal, true
	}

	// 檢查是否為 GKE
	if strings.Contains(endpointLower, "gke") ||
		strings.Contains(endpointLower, "googleapis.com") ||
		strings.Contains(contextLower, "gke") {
		return config.K8sConnectionGKE, false
	}

	// 其他情況視為自定義
	return config.K8sConnectionCustom, false
}

// getConnectionDescription 獲取連線描述
func (cm *ConnectionManager) getConnectionDescription(connType config.K8sConnectionType, contextName string) string {
	switch connType {
	case config.K8sConnectionLocal:
		if strings.Contains(strings.ToLower(contextName), "docker") {
			return "Docker Desktop Kubernetes"
		} else if strings.Contains(strings.ToLower(contextName), "minikube") {
			return "Minikube Local Cluster"
		} else if strings.Contains(strings.ToLower(contextName), "kind") {
			return "Kind Local Cluster"
		} else if strings.Contains(strings.ToLower(contextName), "k3s") {
			return "K3s Local Cluster"
		}
		return "Local Kubernetes Cluster"
	case config.K8sConnectionGKE:
		return "Google Kubernetes Engine"
	case config.K8sConnectionCustom:
		return "Custom Kubernetes Cluster"
	default:
		return "Unknown Kubernetes Cluster"
	}
}

// testConnection 測試連線可用性
func (cm *ConnectionManager) testConnection(contextName string) (bool, error) {
	// 建立臨時配置
	config, err := clientcmd.BuildConfigFromFlags("", cm.config.KubeConfigPath)
	if err != nil {
		return false, err
	}

	// 設定上下文
	if contextName != "" {
		overrides := &clientcmd.ConfigOverrides{
			CurrentContext: contextName,
		}
		config, err = clientcmd.NewNonInteractiveDeferredLoadingClientConfig(
			&clientcmd.ClientConfigLoadingRules{ExplicitPath: cm.config.KubeConfigPath},
			overrides,
		).ClientConfig()
		if err != nil {
			return false, err
		}
	}

	// 設定超時
	config.Timeout = time.Duration(cm.config.ConnectionTimeout) * time.Second

	// 建立客戶端
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return false, err
	}

	// 測試連線 - 嘗試獲取版本資訊
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(cm.config.ConnectionTimeout)*time.Second)
	defer cancel()

	// 使用 context 進行版本查詢
	done := make(chan error, 1)
	go func() {
		_, err := clientset.Discovery().ServerVersion()
		done <- err
	}()

	select {
	case err := <-done:
		if err != nil {
			return false, err
		}
	case <-ctx.Done():
		return false, ctx.Err()
	}

	return true, nil
}

// sortConnectionsByPriority 按優先級排序連線
func (cm *ConnectionManager) sortConnectionsByPriority(connections []ConnectionInfo) []ConnectionInfo {
	if !cm.config.PreferLocal {
		return connections
	}

	var localConnections []ConnectionInfo
	var remoteConnections []ConnectionInfo

	for _, conn := range connections {
		if conn.IsLocal {
			localConnections = append(localConnections, conn)
		} else {
			remoteConnections = append(remoteConnections, conn)
		}
	}

	// 本地連線優先，可用的連線排在前面
	result := make([]ConnectionInfo, 0, len(connections))

	// 先添加可用的本地連線
	for _, conn := range localConnections {
		if conn.Available {
			result = append(result, conn)
		}
	}

	// 再添加不可用的本地連線
	for _, conn := range localConnections {
		if !conn.Available {
			result = append(result, conn)
		}
	}

	// 最後添加遠程連線
	for _, conn := range remoteConnections {
		if conn.Available {
			result = append(result, conn)
		}
	}

	for _, conn := range remoteConnections {
		if !conn.Available {
			result = append(result, conn)
		}
	}

	return result
}

// CreateClientForConnection 為指定連線建立客戶端
func (cm *ConnectionManager) CreateClientForConnection(contextName string) (*kubernetes.Clientset, error) {
	var config *rest.Config
	var err error

	if contextName == "" {
		// 使用預設配置
		config, err = clientcmd.BuildConfigFromFlags("", cm.config.KubeConfigPath)
	} else {
		// 使用指定上下文
		overrides := &clientcmd.ConfigOverrides{
			CurrentContext: contextName,
		}
		config, err = clientcmd.NewNonInteractiveDeferredLoadingClientConfig(
			&clientcmd.ClientConfigLoadingRules{ExplicitPath: cm.config.KubeConfigPath},
			overrides,
		).ClientConfig()
	}

	if err != nil {
		return nil, utils.WrapError(err, utils.ErrCodeNetwork, "failed to build kubeconfig")
	}

	// 設定超時
	config.Timeout = time.Duration(cm.config.ConnectionTimeout) * time.Second

	// 建立客戶端
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, utils.WrapError(err, utils.ErrCodeNetwork, "failed to create K8s client")
	}

	return clientset, nil
}

// GetBestConnection 獲取最佳可用連線
func (cm *ConnectionManager) GetBestConnection() *ConnectionInfo {
	connections := cm.DetectAvailableConnections()

	// 根據配置類型選擇連線
	switch cm.config.ConnectionType {
	case config.K8sConnectionLocal:
		for _, conn := range connections {
			if conn.IsLocal && conn.Available {
				return &conn
			}
		}
	case config.K8sConnectionGKE:
		for _, conn := range connections {
			if conn.Type == config.K8sConnectionGKE && conn.Available {
				return &conn
			}
		}
	case config.K8sConnectionCustom:
		for _, conn := range connections {
			if conn.Type == config.K8sConnectionCustom && conn.Available {
				return &conn
			}
		}
	case config.K8sConnectionAuto:
		// 自動模式：優先本地，然後是任何可用的連線
		for _, conn := range connections {
			if conn.Available {
				return &conn
			}
		}
	}

	// 如果沒有找到可用連線，返回第一個連線（即使不可用）
	if len(connections) > 0 {
		return &connections[0]
	}

	return nil
}
